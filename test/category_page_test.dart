import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:mocktail/mocktail.dart';
import 'package:promobell_landing/src/modules/category/domain/models/category_menu.dart';
import 'package:promobell_landing/src/modules/category/domain/repositories/i_category_repository.dart';
import 'package:promobell_landing/src/modules/category/domain/repositories/i_get_category.dart';
import 'package:promobell_landing/src/modules/category/domain/usecase/get_category_details.dart';
import 'package:promobell_landing/src/modules/category/infra/repositories/category_repository_impl.dart';
import 'package:promobell_landing/src/modules/category/infra/repositories/i_category_datasource.dart';
import 'package:promobell_landing/src/modules/category/presentation/controllers/category_controller.dart';
import 'package:promobell_landing/src/modules/category/presentation/pages/category_page.dart';
import 'package:promobell_landing/src/modules/category/presentation/widgets/category_avatar.dart';
import 'package:promobell_landing/src/modules/category/presentation/widgets/category_profile_card.dart';
import 'package:promobell_landing/src/modules/landing/controller/landing_page_controller.dart';
import 'package:promobell_landing/src/modules/landing/page/empty_state.dart';

/// ------------------------
/// MOCKS
/// ------------------------

class MockLandingPageController extends Mock
    implements LandingPageController {}

class MockCategoryDatasource extends Mock
    implements ICategoryDatasource {}

class MockCategoryController extends Mock
    implements CategoryController {}

/// ------------------------
/// MÓDULO DE TESTE
/// ------------------------
final fakeCategoryController = FakeCategoryController();
final mockDatasource = MockCategoryDatasource();
final mockLandingController = MockLandingPageController();

class CategoryTestModule extends Module {
  @override
  void binds(i) {
    i.addInstance<ICategoryDatasource>(MockCategoryDatasource());
    i.addInstance<LandingPageController>(MockLandingPageController());
    i.addInstance<CategoryController>(fakeCategoryController);
    i.add<IGetCategoryDetails>(
      () => GetCategoryDetails(
        Modular.get(), // repository
      ),
    );
    i.add<ICategoryRepository>(
      () => CategoryRepositoryImpl(Modular.get()),
    );
  }

  @override
  void routes(r) {
    r.child('/', child: (context) => const CategoryPage());
  }
}

/// ------------------------
/// TESTES
/// ------------------------

void main() {
  setUp(() {
    // Reset básico
    fakeCategoryController.setLoading(false);
    fakeCategoryController.setCategory(null);
    fakeCategoryController.setError(null);
    fakeCategoryController.setFollowers(0);
    fakeCategoryController.setProducts(0);
  });

  testWidgets('Mostra CircularProgressIndicator enquanto carrega', (
    tester,
  ) async {
    fakeCategoryController.setLoading(true);

    await tester.pumpWidget(
      ModularApp(
        module: CategoryTestModule(),
        child: MaterialApp.router(routerConfig: Modular.routerConfig),
      ),
    );

    await tester.pump();

    expect(find.byType(CircularProgressIndicator), findsOneWidget);
  });

  testWidgets('Mostra EmptyState quando categoria é null', (
    tester,
  ) async {
    fakeCategoryController.setLoading(false);
    fakeCategoryController.setCategory(null);

    await tester.pumpWidget(
      ModularApp(
        module: CategoryTestModule(),
        child: MaterialApp.router(routerConfig: Modular.routerConfig),
      ),
    );

    await tester.pump();

    expect(find.byType(EmptyState), findsOneWidget);
  });

  testWidgets('Mostra dados da categoria quando carregada', (
    tester,
  ) async {
    final fakeCategory = CategoryMenu(
      id: 18,
      nome: 'Categoria Teste',
      fotoPequena:
          'assets/categorias/beleza_pura/category-image-belezapura.png',
      bio: 'Descrição da categoria',
      cor: const Color(0xFF123456),
    );

    fakeCategoryController.setLoading(false);
    fakeCategoryController.setCategory(fakeCategory);
    fakeCategoryController.setFollowers(42);
    fakeCategoryController.setProducts(12);
    fakeCategoryController.setError(null);

    await tester.pumpWidget(
      ModularApp(
        module: CategoryTestModule(),
        child: MaterialApp.router(routerConfig: Modular.routerConfig),
      ),
    );

    await tester.pump();

    expect(find.text('Categoria Teste'), findsOneWidget);
    expect(find.text('Descrição da categoria'), findsOneWidget);
    expect(find.byType(CategoryProfileCard), findsOneWidget);
    expect(find.byType(CategoryAvatar), findsOneWidget);
  });
}

class FakeCategoryController extends ChangeNotifier
    implements CategoryController {
  bool _loading = false;
  CategoryMenu? _category;
  String? _error;
  int _followers = 0;
  int _products = 0;

  @override
  bool get loading => _loading;
  @override
  CategoryMenu? get category => _category;
  @override
  String? get error => _error;
  @override
  int get followers => _followers;
  @override
  int get products => _products;

  @override
  Future<void> loadCategoryData(int? id) async {
    return;
  }

  void setLoading(bool value) {
    _loading = value;
    notifyListeners();
  }

  void setCategory(CategoryMenu? category) {
    _category = category;
    notifyListeners();
  }

  void setError(String? value) {
    _error = value;
    notifyListeners();
  }

  void setFollowers(int value) {
    _followers = value;
    notifyListeners();
  }

  void setProducts(int value) {
    _products = value;
    notifyListeners();
  }
}
