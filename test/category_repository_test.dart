// import 'package:flutter/material.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:mocktail/mocktail.dart';
// import 'package:promobell_landing/src/core/enums/enums_errors.dart';
// import 'package:promobell_landing/src/modules/category/domain/models/categoria_menu.dart';
// import 'package:promobell_landing/src/modules/category/infra/repositories/category_repository_impl.dart';
// import 'package:promobell_landing/src/modules/category/infra/repositories/i_category_datasource.dart';

// class MockCategoryDatasource extends Mock implements ICategoryDatasource {}

// void main() {
//   late MockCategoryDatasource mockDatasource;
//   late CategoryRepositoryImpl repository;

//   setUp(() {
//     mockDatasource = MockCategoryDatasource();
//     repository = CategoryRepositoryImpl(mockDatasource);
//   });

//   test('Retorna erro quando datasource retorna null (categoria não encontrada)', () async {
//     // Simula o datasource retornando null
//     when(() => mockDatasource.getCategory(any())).thenAnswer((_) async => null);

//     final result = await repository.getCategoryDetails(123);

//     expect(result.isLeft(), true);
//     expect(result.fold((l) => l, (r) => null), CategoryError.categoryNotFound);
//   });

//   test('Retorna categoria quando datasource retorna dados válidos', () async {
//     final fakeCategory = CategoryMenu(id: 1, nome: 'Teste', fotoPequena: 'url', bio: 'bio', cor: Colors.red);

//     when(() => mockDatasource.getCategory(any())).thenAnswer((_) async => fakeCategory);

//     final result = await repository.getCategoryDetails(1);

//     expect(result.isRight(), true);
//     expect(result.fold((l) => null, (r) => r), fakeCategory);
//   });
// }
