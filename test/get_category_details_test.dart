// import 'dart:ui';

// import 'package:dartz/dartz.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:mocktail/mocktail.dart';
// import 'package:promobell_landing/src/core/enums/enums_errors.dart';
// import 'package:promobell_landing/src/modules/category/domain/models/categoria_menu.dart';
// import 'package:promobell_landing/src/modules/category/domain/repositories/i_category_repository.dart';
// import 'package:promobell_landing/src/modules/category/domain/usecase/get_category_details.dart';
// import 'package:promobell_landing/src/modules/category/infra/repositories/i_category_datasource.dart';

// class MockRepository extends Mock implements ICategoryRepository {}

// class MockDatasource extends Mock implements ICategoryDatasource {}

// void main() {
//   late GetCategoryDetails useCase;
//   late MockRepository mockRepository;
//   late MockDatasource mockDatasource;

//   setUp(() {
//     mockRepository = MockRepository();
//     mockDatasource = MockDatasource();
//     useCase = GetCategoryDetails(mockRepository, mockDatasource);
//   });

//   test('Retorna CategoryDetails quando tudo está certo', () async {
//     final category = CategoryMenu(
//       id: 1,
//       nome: 'Promoções',
//       fotoPequena: 'foto.jpg',
//       bio: 'bio',
//       cor: const Color(0xFF000000),
//     );

//     when(() => mockRepository.getCategoryDetails(1)).thenAnswer((_) async => Right(category));

//     when(() => mockDatasource.getCategoryFollowersCount(1)).thenAnswer((_) async => 42);

//     when(() => mockDatasource.getTotalProductsByCategory('Promoções')).thenAnswer((_) async => 17);

//     final result = await useCase(1);

//     expect(result.isRight(), true);
//     result.fold((l) => fail('Não era esperado erro'), (r) {
//       expect(r.category, category);
//       expect(r.followers, 42);
//       expect(r.products, 17);
//     });
//   });

//   test('Retorna erro se o repositório falhar', () async {
//     when(() => mockRepository.getCategoryDetails(1)).thenAnswer((_) async => Left(CategoryError.categoryNotFound));

//     final result = await useCase(1);

//     expect(result.isLeft(), true);
//     expect(result.fold((l) => l, (r) => null), CategoryError.categoryNotFound);
//   });
// }
