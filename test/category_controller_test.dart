// import 'package:dartz/dartz.dart';
// import 'package:flutter/material.dart';
// import 'package:flutter_test/flutter_test.dart';
// import 'package:mocktail/mocktail.dart';
// import 'package:promobell_landing/src/core/enums/enums_errors.dart';
// import 'package:promobell_landing/src/modules/category/domain/models/categoria_menu.dart';
// import 'package:promobell_landing/src/modules/category/domain/models/category_details.dart';
// import 'package:promobell_landing/src/modules/category/domain/repositories/i_get_category.dart';
// import 'package:promobell_landing/src/modules/category/presentation/controllers/category_controller.dart';

// class MockGetCategoryDetails extends Mock implements IGetCategoryDetails {}

// void main() {
//   late CategoryController controller;
//   late MockGetCategoryDetails mockUseCase;

//   setUp(() {
//     mockUseCase = MockGetCategoryDetails();
//     controller = CategoryController(getCategoryDetails: mockUseCase);
//   });

//   test('Quando ID é nulo, seta erro e zera estados', () async {
//     await controller.loadCategoryData(null);

//     expect(controller.error, 'ID inválido');
//     expect(controller.category, isNull);
//     expect(controller.followers, 0);
//     expect(controller.products, 0);
//   });

//   test('Quando recebe erro do use case, seta erro corretamente', () async {
//     when(() => mockUseCase.call(any())).thenAnswer((_) async => Left(CategoryError.errorLoading));

//     await controller.loadCategoryData(123);

//     expect(controller.error, isNotNull);
//     expect(controller.category, isNull);
//     expect(controller.followers, 0);
//     expect(controller.products, 0);
//   });

//   test('Quando recebe sucesso, preenche dados corretamente', () async {
//     final category = CategoryMenu(
//       id: 1,
//       nome: 'Categoria Top',
//       fotoPequena: 'urlzinha',
//       bio: 'top demais',
//       cor: Colors.purple,
//     );

//     final details = CategoryDetails(category, 55, 88);

//     when(() => mockUseCase.call(1)).thenAnswer((_) async => Right(details));

//     await controller.loadCategoryData(1);

//     expect(controller.error, isNull);
//     expect(controller.category, equals(category));
//     expect(controller.followers, 55);
//     expect(controller.products, 88);
//   });
// }
