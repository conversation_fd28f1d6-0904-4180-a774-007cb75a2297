import 'package:flutter/cupertino.dart';

class ResponsiveScaleLayout extends StatelessWidget {
  final Widget Function(
    BuildContext context,
    double screenWidth,
    double itemWidth,
    double scaleFactor,
  ) builder;

  const ResponsiveScaleLayout({super.key, required this.builder});

  @override
  Widget build(BuildContext context) {
    return LayoutBuilder(
      builder: (context, constraints) {
        final double screenWidth = constraints.maxWidth;
        final double itemGap = 16.0;
        final double maxItemWidth = 248;

        final double itemWidth = ((screenWidth - itemGap) / 2).clamp(100.0, maxItemWidth);
        final double scaleFactor = itemWidth / maxItemWidth;

        return builder(context, screenWidth, itemWidth, scaleFactor);
      },
    );
  }
}
