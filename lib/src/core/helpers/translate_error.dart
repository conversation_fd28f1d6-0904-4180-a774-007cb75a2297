import '../enums/enums_errors.dart';

String translateProductError(ProductError error) {
  switch (error) {
    case ProductError.idNotProvided:
      return 'ID do produto não foi informado.';
    case ProductError.invalidId:
      return 'O ID do produto é inválido.';
    case ProductError.serverFailure:
      return 'Erro ao buscar produto.';
  }
}

String translateCategoryError(CategoryError error) {
  switch (error) {
    case CategoryError.idNotProvided:
      return 'ID da categoria não fornecido';
    case CategoryError.invalidId:
      return 'ID da categoria inválido';
    case CategoryError.categoryNotFound:
      return 'Erro ao carregar a categoria: $error';
    case CategoryError.errorLoading:
      return 'Erro ao carregar a categoria: $error';
  }
}
