import '../theme/svg_icons.dart';

class PlatformIcons {
  static const Map<String, String> _icons = {
    'mercado livre': SvgIcons.mercadoLivreLogoGrande,
    'mercadolivre': SvgIcons.mercadoLivreLogoGrande,
    'magazine luiza': SvgIcons.magaluLogoGrande,
    'magazineluiza': SvgIcons.magaluLogoGrande,
    'magalu': SvgIcons.magaluLogoGrande,
    'amazon': SvgIcons.amazonLogoGrande,
  };

  static String fromName(String name) {
    final normalized = name.trim().toLowerCase();
    return _icons[normalized] ?? SvgIcons.amazonLogoGrande;
  }
}
