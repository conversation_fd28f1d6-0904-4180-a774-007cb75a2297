import 'package:flutter/gestures.dart';
import 'package:flutter/material.dart';

import 'color_outlet.dart';

enum FontWeightOption { black, bold, extraBold, extraLight, light, medium, regular, semiBold, thin }

enum FontFamilyOption { roboto, figtree }

class TextPattern {
  static const String _defaultFontFamily = 'Figtree';
  static const Color _defaultColor = ColorOutlet.contentSecondary;

  get defaultColor => _defaultColor;
  get fontFamily => _defaultFontFamily;

  static Widget customText({
    required String text,
    double fontSize = 14,
    TextAlign textAlign = TextAlign.start,
    FontWeightOption fontWeightOption = FontWeightOption.regular,
    FontFamilyOption fontFamilyOption = FontFamilyOption.figtree,
    Color color = _defaultColor,
    double? lineHeight,
    int? maxLines,
    bool? softWrap,
    TextOverflow? overflow,
    TextDecoration? decoration,
    Color? decorationColor,
    TextDecorationStyle? decorationStyle,
    FontStyle? fontStyle,
    bool isSelectable = false,
  }) {
    final calculatedLineHeight = lineHeight ?? _getLineHeight(fontSize, fontWeightOption);

    final textStyle = TextStyle(
      decoration: decoration,
      decorationColor: decorationColor,
      fontSize: fontSize,
      fontFamily: _mapFontFamilyOption(fontFamilyOption),
      color: color,
      fontWeight: _mapFontWeightOption(fontWeightOption),
      fontStyle: fontStyle,
      letterSpacing: 0,
      height: calculatedLineHeight,
      overflow: overflow,
      decorationStyle: decorationStyle,
    );

    if (isSelectable) {
      return SelectionArea(
        child: Text(text, textAlign: textAlign, maxLines: maxLines, softWrap: softWrap, style: textStyle),
      );
    } else {
      return Text(text, textAlign: textAlign, maxLines: maxLines, softWrap: softWrap, style: textStyle);
    }
  }

  static double _getLineHeight(double fontSize, FontWeightOption fontWeightOption) {
    if (fontSize == 24 && fontWeightOption == FontWeightOption.bold) return 1.5;
    if (fontSize == 20 && fontWeightOption == FontWeightOption.bold) return 1.25;
    if (fontSize == 16) return 1.5;
    if (fontSize == 14) return 1.25;
    if (fontSize == 12) return 1.0;
    if (fontSize == 11) return 0.875;
    return 1.25;
  }

  static FontWeight _mapFontWeightOption(FontWeightOption option) {
    switch (option) {
      case FontWeightOption.black:
        return FontWeight.w900;
      case FontWeightOption.bold:
        return FontWeight.w700;
      case FontWeightOption.extraBold:
        return FontWeight.w800;
      case FontWeightOption.extraLight:
        return FontWeight.w200;
      case FontWeightOption.light:
        return FontWeight.w300;
      case FontWeightOption.medium:
        return FontWeight.w500;
      case FontWeightOption.semiBold:
        return FontWeight.w600;
      case FontWeightOption.thin:
        return FontWeight.w100;
      default:
        return FontWeight.w400;
    }
  }

  static String _mapFontFamilyOption(FontFamilyOption option) {
    switch (option) {
      case FontFamilyOption.roboto:
        return 'Roboto';
      case FontFamilyOption.figtree:
        return 'Figtree';
    }
  }

  static Widget customRichText({
    required List<InlineSpan> children,
    double fontSize = 14,
    TextAlign textAlign = TextAlign.start,
    FontFamilyOption fontFamilyOption = FontFamilyOption.figtree,
    Color color = _defaultColor,
    double? lineHeight,
    bool isSelectable = false,
    TextOverflow? overflow,
  }) {
    final calculatedLineHeight = lineHeight ?? _getLineHeight(fontSize, FontWeightOption.regular);

    final baseStyle = TextStyle(
      fontSize: fontSize,
      fontFamily: _mapFontFamilyOption(fontFamilyOption),
      color: color,
      height: calculatedLineHeight,
      overflow: overflow,
    );

    if (isSelectable) {
      return SelectableText.rich(TextSpan(style: baseStyle, children: children), textAlign: textAlign);
    } else {
      return RichText(textAlign: textAlign, text: TextSpan(style: baseStyle, children: children));
    }
  }
}

List<InlineSpan> highlightWords({
  required BuildContext context,
  required String text,
  required List<String> wordsToHighlight,
  TextStyle? normalStyle,
  TextStyle? highlightStyle,
  void Function(String word)? onTapWord, // Função opcional
}) {
  final spans = <InlineSpan>[];

  final pattern = RegExp('(${wordsToHighlight.join('|')})', caseSensitive: false);

  text.splitMapJoin(
    pattern,
    onMatch: (match) {
      final matchedWord = match[0]!;

      final styleToUse = highlightStyle ?? const TextStyle(fontWeight: FontWeight.bold, color: Colors.blue);

      spans.add(
        TextSpan(
          text: matchedWord,
          style: styleToUse.copyWith(decoration: onTapWord != null ? TextDecoration.underline : null),
          recognizer: onTapWord != null ? (TapGestureRecognizer()..onTap = () => onTapWord(matchedWord)) : null,
        ),
      );
      return '';
    },
    onNonMatch: (nonMatch) {
      spans.add(TextSpan(text: nonMatch, style: normalStyle));
      return '';
    },
  );

  return spans;
}
