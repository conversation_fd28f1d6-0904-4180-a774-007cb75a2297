import 'package:flutter_modular/flutter_modular.dart';

import '../../core/bindings/core_module.dart';
import 'domain/repositories/i_get_product.dart';
import 'domain/repositories/i_product_repository.dart';
import 'domain/usecase/get_products.dart';
import 'infra/data/product_datasource_impl.dart';
import 'infra/repositories/i_product_datasource.dart';
import 'infra/repositories/product_repository_impl.dart';

class ProductModule extends Module {
  @override
  List<Module> get imports => [CoreModule()];

  @override
  void exportedBinds(i) {
    i.addSingleton<IProductDatasource>(ProductDatasourceImpl.new);
    i.addSingleton<IProductRepository>(ProductRepositoryImpl.new);
    i.addSingleton<IGetProduct>(GetProduct.new);
  }
}
