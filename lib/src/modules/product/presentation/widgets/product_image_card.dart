import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:promobell_landing/src/core/theme/color_outlet.dart';
import 'package:promobell_landing/src/core/theme/svg_icons.dart';

class ProductImageCard extends StatelessWidget {
  final String? imageUrl;

  const ProductImageCard({required this.imageUrl, super.key});

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      print('imageUrl: $imageUrl');
    }
    return Container(
      height: 216,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: ColorOutlet.surface,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: CachedNetworkImage(
          imageUrl: imageUrl!,
          height: 216,
          width: 326,
          fit: BoxFit.contain,
          progressIndicatorBuilder:
              (context, url, progress) => Center(
                child: CircularProgressIndicator(
                  value: progress.progress,
                  color: ColorOutlet.contentPrimary,
                  strokeWidth: 1.5,
                ),
              ),
          errorWidget: (context, url, error) {
            if (kDebugMode) {
              print('Error loading image: $error');
            }
            return Center(
              child: SizedBox(
                width: 24,
                height: 24,
                child: FittedBox(
                  child: SvgPicture.asset(
                    SvgIcons.archiveImageBroken,
                  ),
                ),
              ),
            );
          },
        ),
      ),
    );
  }
}
