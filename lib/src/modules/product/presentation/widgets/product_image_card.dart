import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:promobell_landing/src/core/theme/color_outlet.dart';
import 'package:promobell_landing/src/core/theme/svg_icons.dart';

class ProductImageCard extends StatefulWidget {
  final String? imageUrl;

  const ProductImageCard({required this.imageUrl, super.key});

  @override
  State<ProductImageCard> createState() => _ProductImageCardState();
}

class _ProductImageCardState extends State<ProductImageCard> {
  bool _hasError = false;
  bool _tryingFallback = false;

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      print('imageUrl: ${widget.imageUrl}');
    }

    if (widget.imageUrl == null || widget.imageUrl!.isEmpty) {
      return _buildErrorWidget();
    }

    return Container(
      height: 216,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: ColorOutlet.surface,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: _hasError ? _buildErrorWidget() : _buildImageWidget(),
      ),
    );
  }

  Widget _buildImageWidget() {
    String imageUrl = widget.imageUrl!;

    // Se está tentando fallback, converte webp para jpg
    if (_tryingFallback && imageUrl.endsWith('.webp')) {
      imageUrl = imageUrl.replaceAll('.webp', '.jpg');
      if (kDebugMode) {
        print('Trying fallback URL: $imageUrl');
      }
    }

    return Image.network(
      imageUrl,
      height: 216,
      width: 326,
      fit: BoxFit.contain,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Center(
          child: CircularProgressIndicator(
            value:
                loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        (loadingProgress.expectedTotalBytes ?? 1)
                    : null,
            color: ColorOutlet.contentPrimary,
            strokeWidth: 1.5,
          ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        if (kDebugMode) {
          print('Image.network failed: $error');
          print('Failed URL: $imageUrl');
          print('Error type: ${error.runtimeType}');
          print('Trying fallback: $_tryingFallback');
        }

        // Se ainda não tentou fallback e a URL é webp, tenta com jpg
        if (!_tryingFallback && widget.imageUrl!.endsWith('.webp')) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _tryingFallback = true;
              });
            }
          });
          return Center(
            child: CircularProgressIndicator(
              color: ColorOutlet.contentPrimary,
              strokeWidth: 1.5,
            ),
          );
        }

        // Marca como erro final
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              _hasError = true;
            });
          }
        });

        return _buildErrorWidget();
      },
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 48,
            height: 48,
            child: SvgPicture.asset(
              SvgIcons.archiveImageBroken,
              colorFilter: ColorFilter.mode(
                Colors.grey[400]!,
                BlendMode.srcIn,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Imagem não disponível',
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
          ),
        ],
      ),
    );
  }
}
