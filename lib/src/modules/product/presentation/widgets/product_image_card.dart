import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:promobell_landing/src/core/theme/color_outlet.dart';
import 'package:promobell_landing/src/core/theme/svg_icons.dart';

class ProductImageCard extends StatefulWidget {
  final String? imageUrl;

  const ProductImageCard({required this.imageUrl, super.key});

  @override
  State<ProductImageCard> createState() => _ProductImageCardState();
}

class _ProductImageCardState extends State<ProductImageCard> {
  bool _hasError = false;
  int _attemptCount = 0;
  static const int _maxAttempts = 3;

  @override
  Widget build(BuildContext context) {
    if (kDebugMode) {
      print('imageUrl: ${widget.imageUrl}');
    }

    if (widget.imageUrl == null || widget.imageUrl!.isEmpty) {
      return _buildErrorWidget();
    }

    return Container(
      height: 216,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: ColorOutlet.surface,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: _hasError ? _buildErrorWidget() : _buildImageWidget(),
      ),
    );
  }

  String _getImageUrl(String originalUrl, int attempt) {
    switch (attempt) {
      case 0:
        // Primeira tentativa: URL original
        return originalUrl;
      case 1:
        // Segunda tentativa: Converte webp para jpg
        if (originalUrl.endsWith('.webp')) {
          return originalUrl.replaceAll('.webp', '.jpg');
        }
        return originalUrl;
      case 2:
        // Terceira tentativa: Usa proxy CORS (apenas em desenvolvimento)
        if (kDebugMode) {
          return 'https://api.allorigins.win/raw?url=${Uri.encodeComponent(originalUrl)}';
        }
        return originalUrl;
      default:
        return originalUrl;
    }
  }

  Widget _buildImageWidget() {
    String imageUrl = _getImageUrl(widget.imageUrl!, _attemptCount);

    if (kDebugMode) {
      print('Attempt $_attemptCount: $imageUrl');
    }

    return Image.network(
      imageUrl,
      height: 216,
      width: 326,
      fit: BoxFit.contain,
      loadingBuilder: (context, child, loadingProgress) {
        if (loadingProgress == null) return child;
        return Center(
          child: CircularProgressIndicator(
            value:
                loadingProgress.expectedTotalBytes != null
                    ? loadingProgress.cumulativeBytesLoaded /
                        (loadingProgress.expectedTotalBytes ?? 1)
                    : null,
            color: ColorOutlet.contentPrimary,
            strokeWidth: 1.5,
          ),
        );
      },
      errorBuilder: (context, error, stackTrace) {
        if (kDebugMode) {
          print(
            'Image.network failed (attempt $_attemptCount): $error',
          );
          print('Failed URL: $imageUrl');
          print('Error type: ${error.runtimeType}');
        }

        // Tenta próxima estratégia se ainda há tentativas
        if (_attemptCount < _maxAttempts - 1) {
          WidgetsBinding.instance.addPostFrameCallback((_) {
            if (mounted) {
              setState(() {
                _attemptCount++;
              });
            }
          });
          return Center(
            child: CircularProgressIndicator(
              color: ColorOutlet.contentPrimary,
              strokeWidth: 1.5,
            ),
          );
        }

        // Marca como erro final
        WidgetsBinding.instance.addPostFrameCallback((_) {
          if (mounted) {
            setState(() {
              _hasError = true;
            });
          }
        });

        return _buildErrorWidget();
      },
    );
  }

  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          SizedBox(
            width: 48,
            height: 48,
            child: SvgPicture.asset(
              SvgIcons.archiveImageBroken,
              colorFilter: ColorFilter.mode(
                Colors.grey[400]!,
                BlendMode.srcIn,
              ),
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Imagem não disponível',
            style: TextStyle(color: Colors.grey[600], fontSize: 12),
          ),
        ],
      ),
    );
  }
}
