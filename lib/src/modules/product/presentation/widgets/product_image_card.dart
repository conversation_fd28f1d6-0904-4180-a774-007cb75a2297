import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:promobell_landing/src/core/theme/color_outlet.dart';
import 'package:promobell_landing/src/core/theme/svg_icons.dart';

class ProductImageCard extends StatelessWidget {
  final String? imageUrl;

  const ProductImageCard({required this.imageUrl, super.key});

  @override
  Widget build(BuildContext context) {
    print('imageUrl: $imageUrl');
    return Container(
      height: 216,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(16),
        color: ColorOutlet.surface,
      ),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(16),
        child: CachedNetworkImage(
          imageUrl: imageUrl!,
          height: 216,
          width: 326,
          fit: BoxFit.contain,
          color: ColorOutlet.surface,
          colorBlendMode: BlendMode.multiply,
          progressIndicatorBuilder:
              (context, url, progress) => Center(
                child: CircularProgressIndicator(
                  value: progress.progress,
                  color: ColorOutlet.contentPrimary,
                  strokeWidth: 1.5,
                ),
              ),
          errorWidget:
              (context, url, error) => Center(
                child: SizedBox(
                  width: 24,
                  height: 24,
                  child: FittedBox(
                    child: SvgPicture.asset(
                      SvgIcons.archiveImageBroken,
                    ),
                  ),
                ),
              ),
        ),
      ),
    );
  }
}

// import 'package:flutter/material.dart';
// import 'package:flutter_svg/flutter_svg.dart';
// import 'package:promobell_landing/src/core/theme/color_outlet.dart';
// import 'package:promobell_landing/src/core/theme/svg_icons.dart';

// class ProductImageCard extends StatelessWidget {
//   final String? imageUrl;

//   const ProductImageCard({required this.imageUrl, super.key});

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       height: 216,
//       decoration: BoxDecoration(
//         borderRadius: BorderRadius.circular(16),
//         color: ColorOutlet.surface,
//       ),
//       child: ClipRRect(
//         borderRadius: BorderRadius.circular(16),
//         child:
//             imageUrl != null
//                 ? Image.network(
//                   imageUrl!,
//                   height: 216,
//                   width: 326,
//                   fit: BoxFit.contain,
//                   color: ColorOutlet.surface,
//                   colorBlendMode: BlendMode.multiply,
//                   loadingBuilder: (context, child, loadingProgress) {
//                     if (loadingProgress == null) return child;
//                     return Center(
//                       child: CircularProgressIndicator(
//                         value:
//                             loadingProgress.expectedTotalBytes != null
//                                 ? loadingProgress
//                                         .cumulativeBytesLoaded /
//                                     (loadingProgress
//                                             .expectedTotalBytes ??
//                                         1)
//                                 : null,
//                         color: ColorOutlet.contentPrimary,
//                         strokeWidth: 1.5,
//                       ),
//                     );
//                   },
//                   errorBuilder:
//                       (context, error, stackTrace) => Center(
//                         child: SizedBox(
//                           width: 24,
//                           height: 24,
//                           child: FittedBox(
//                             child: SvgPicture.asset(
//                               SvgIcons.archiveImageBroken,
//                             ),
//                           ),
//                         ),
//                       ),
//                 )
//                 : Center(
//                   child: SizedBox(
//                     width: 24,
//                     height: 24,
//                     child: FittedBox(
//                       child: SvgPicture.asset(
//                         SvgIcons.archiveImageBroken,
//                       ),
//                     ),
//                   ),
//                 ),
//       ),
//     );
//   }
// }
