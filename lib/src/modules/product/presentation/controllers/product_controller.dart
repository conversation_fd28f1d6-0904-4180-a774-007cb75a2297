import 'package:flutter/widgets.dart';
import 'package:promobell_landing/src/core/enums/enums_errors.dart';
import 'package:promobell_landing/src/core/helpers/translate_error.dart';

import '../../domain/models/product.dart';
import '../../domain/repositories/i_get_product.dart';

class ProductController with ChangeNotifier {
  final IGetProduct _getProduct;

  ProductController({required IGetProduct getProduct}) : _getProduct = getProduct;

  Product? _product;
  Product? get product => _product;

  bool _isLoading = true;
  bool get loading => _isLoading;

  String? _errorMessage;
  String? get errorMessage => _errorMessage;

  Future<void> loadProductData(int? id) async {
    _isLoading = true;
    _errorMessage = null;
    notifyListeners();

    if (id == null) {
      _errorMessage = translateProductError(ProductError.invalidId);
      _isLoading = false;
      notifyListeners();
      return;
    }

    final result = await _getProduct(id);
    result.fold((error) => _errorMessage = translateProductError(error), (product) => _product = product);

    _isLoading = false;
    notifyListeners();
  }
}
