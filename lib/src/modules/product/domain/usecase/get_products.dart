import 'package:dartz/dartz.dart';
import 'package:promobell_landing/src/core/enums/enums_errors.dart';
import 'package:promobell_landing/src/modules/product/domain/repositories/i_get_product.dart';

import '../models/product.dart';
import '../repositories/i_product_repository.dart';

class GetProduct implements IGetProduct {
  final IProductRepository repository;

  GetProduct(this.repository);

  @override
  Future<Either<ProductError, Product?>> call(int id) async {
    if (id <= 0) return Left(ProductError.invalidId);

    try {
      final product = await repository.getProduct(id);
      if (product == null) return Left(ProductError.idNotProvided);
      return Right(product);
    } catch (e) {
      return Left(ProductError.serverFailure);
    }
  }
}
