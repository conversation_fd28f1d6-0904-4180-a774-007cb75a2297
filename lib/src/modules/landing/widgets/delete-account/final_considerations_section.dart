import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';

import '../../../../core/theme/color_outlet.dart';
import '../../../../core/theme/text_pattern.dart';

class ConsiderationsSection extends StatelessWidget {
  final bool isMobile;
  final String title;
  final String subtitle;
  final double paddingEnd;

  const ConsiderationsSection({
    this.paddingEnd = 156,
    required this.title,
    required this.subtitle,
    this.isMobile = false,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        TextPattern.customText(
          text: title,
          color: ColorOutlet.contentPrimary,
          fontWeightOption: FontWeightOption.bold,
          fontSize: 48,
        ),
        const SizedBox(height: 32),
        TextPattern.customRichText(
          fontSize: 16,
          isSelectable: true,
          children: highlightWords(
            onTapWord: (word) {
              Modular.to.pushNamed('/privacidade');
            },
            context: context,
            text: subtitle,
            wordsToHighlight: ['Política de Privacidade'],
            normalStyle: TextStyle(
              color: ColorOutlet.contentSecondary,
              fontSize: 16,
              fontFamily: TextPattern().fontFamily,
              fontWeight: FontWeight.w400,
            ),
            highlightStyle: TextStyle(
              color: ColorOutlet.contentPrimary,
              fontSize: 16,
              fontFamily: TextPattern().fontFamily,
              fontWeight: FontWeight.w400,
              decoration: TextDecoration.underline,
              decorationColor: ColorOutlet.contentPrimary,
            ),
          ),
        ),
        SizedBox(height: isMobile ? 222 : paddingEnd),
      ],
    );
  }
}
