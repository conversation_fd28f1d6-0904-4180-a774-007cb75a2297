import 'package:flutter/material.dart';

import '../../../../core/theme/color_outlet.dart';
import '../../../../core/theme/text_pattern.dart';
import '../../../../core/widgets/layout/custom_line_spacer.dart';

class SectionBlock extends StatelessWidget {
  final String title;
  final List<Widget> children;
  final bool isEnd;
  final bool isDesktop;

  const SectionBlock({
    required this.title,
    required this.children,
    this.isEnd = false,
    this.isDesktop = true,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return isDesktop
        ? Column(
          children: [
            Row(
              spacing: 16,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Expanded(
                  flex: 2,
                  child: TextPattern.customText(
                    isSelectable: true,
                    text: title,
                    fontSize: 32,
                    color: ColorOutlet.contentPrimary,
                    fontWeightOption: FontWeightOption.bold,
                  ),
                ),
                Flexible(
                  flex: 2,
                  child: Column(spacing: 20, crossAxisAlignment: CrossAxisAlignment.start, children: children),
                ),
              ],
            ),
            if (!isEnd) CustomLineSpacer(),
          ],
        )
        : Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            TextPattern.customText(
              isSelectable: true,
              text: title,
              fontSize: 32,
              color: ColorOutlet.contentPrimary,
              fontWeightOption: FontWeightOption.bold,
            ),
            const SizedBox(height: 16),
            Column(spacing: 20, crossAxisAlignment: CrossAxisAlignment.start, children: children),
            if (!isEnd) CustomLineSpacer(),
          ],
        );
  }
}
