import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell_landing/src/modules/landing/controller/landing_page_controller.dart';

import '../../../../core/theme/color_outlet.dart';
import '../../../../core/theme/text_pattern.dart';

class CustomRichText extends StatelessWidget {
  final String text;
  final bool isPrimaryColor;

  const CustomRichText({this.isPrimaryColor = false, required this.text, super.key});

  @override
  Widget build(BuildContext context) {
    final controller = Modular.get<LandingPageController>();

    return isPrimaryColor
        ? TextPattern.customRichText(
          fontSize: 16,
          isSelectable: true,
          children: highlightWords(
            context: context,
            text: text,
            onTapWord: (word) {
              if (word == 'canal oficial no WhatsApp') {
                controller.launchWhatsApp();
              }
              if (word == 'instagram.com/promobelloficial') {
                controller.launchInstagram();
              }
              if (word == 'Excluir sua conta') {
                Modular.to.pushNamed('/excluir-conta');
              }
              if (word == 'Política de Privacidade') {
                Modular.to.pushNamed('/privacidade');
              }
            },
            wordsToHighlight: [
              'canal oficial no WhatsApp',
              'instagram.com/promobelloficial',
              '<EMAIL>',
              'Excluir sua conta',
              'Política de Privacidade',
            ],
            normalStyle: TextStyle(
              color: ColorOutlet.contentSecondary,
              fontSize: 16,
              fontFamily: TextPattern().fontFamily,
              fontWeight: FontWeight.w400,
            ),
            highlightStyle: TextStyle(
              color: ColorOutlet.contentPrimary,
              decorationColor: ColorOutlet.contentPrimary,
              decoration: TextDecoration.underline,
              fontSize: 16,
              fontFamily: TextPattern().fontFamily,
              fontWeight: FontWeight.w400,
            ),
          ),
        )
        : TextPattern.customRichText(
          fontSize: 16,
          isSelectable: true,
          children: highlightWords(
            context: context,
            text: text,

            wordsToHighlight: ['Promobell', 'Amazon', 'Mercado Livre', 'Magazine Luiza', 'WhatsApp', 'Telegram'],
            normalStyle: TextStyle(
              color: ColorOutlet.contentSecondary,
              fontSize: 16,
              fontFamily: TextPattern().fontFamily,
              fontWeight: FontWeight.w400,
            ),
            highlightStyle: TextStyle(
              color: ColorOutlet.contentSecondary,
              fontSize: 16,
              fontFamily: TextPattern().fontFamily,
              fontWeight: FontWeight.bold,
            ),
          ),
        );
  }
}
