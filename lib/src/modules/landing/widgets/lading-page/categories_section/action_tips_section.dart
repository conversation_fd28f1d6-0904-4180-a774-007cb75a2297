import 'dart:math' as math;

import 'package:flutter/material.dart';

import '../../../../../core/theme/color_outlet.dart';
import '../../../../../core/theme/text_pattern.dart';
import '../../../../../core/widgets/layout/responsive_scale_layout.dart';
import '../../../../../core/widgets/layout/scroll_reveal.dart';

class ActionTipsSection extends StatelessWidget {
  final bool center;

  final bool isMobile;

  const ActionTipsSection({this.center = false, this.isMobile = false, super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsiveScaleLayout(
      builder: (context, screenWidth, itemWidth, scaleFactor) {
        final textScaler = MediaQuery.textScalerOf(context);
        final double scaleImages = (48 * scaleFactor).clamp(34, 48);

        return ConstrainedBox(
          constraints: const BoxConstraints(maxWidth: 530),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              ScrollReveal(
                child: TextPattern.customText(
                  text: 'Curta, salve ou\ncompartilhe',
                  fontSize: isMobile ? math.max(32, textScaler.scale(48 * scaleFactor)) : 48,
                  fontWeightOption: FontWeightOption.bold,
                  color: ColorOutlet.contentSecondary,
                  textAlign: TextAlign.start,
                ),
              ),
              const SizedBox(height: 16),
              ScrollReveal(
                offset: Offset(0, 1),
                child: TextPattern.customText(
                  text: 'Curtiu uma oferta? Salve ou compartilhe para não perder na hora de comprar.',
                  fontSize: isMobile ? math.max(16, textScaler.scale(20 * scaleFactor)) : 20,
                  fontWeightOption: FontWeightOption.regular,
                  color: ColorOutlet.contentSecondary,
                  textAlign: TextAlign.start,
                ),
              ),
              const SizedBox(height: 32),
              Row(
                spacing: 16,
                children: [
                  ScrollReveal(
                    offset: Offset(-3, 0),
                    child: Image.asset('assets/images/favorite.png', width: scaleImages, height: scaleImages),
                  ),
                  ScrollReveal(
                    offset: Offset(-2, 0),
                    child: Image.asset('assets/images/bookmark.png', width: scaleImages, height: scaleImages),
                  ),
                  ScrollReveal(
                    offset: Offset(-1, 0),
                    child: Image.asset('assets/images/share.png', width: scaleImages, height: scaleImages),
                  ),
                ],
              ),
              const SizedBox(height: 48),
            ],
          ),
        );
      },
    );
  }
}
