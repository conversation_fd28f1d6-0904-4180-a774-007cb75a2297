import 'package:flutter/material.dart';

import '../../../../../core/theme/color_outlet.dart';
import '../../../../../core/widgets/layout/responsive_padding_wrappe.dart';
import 'category_follow_promo.dart';
import 'category_grid.dart';

class CategoriesSection extends StatelessWidget {
  final VoidCallback onAppStorePressed;
  final VoidCallback onGooglePlayPressed;

  const CategoriesSection({super.key, required this.onAppStorePressed, required this.onGooglePlayPressed});

  @override
  Widget build(BuildContext context) {
    final isDesktop = MediaQuery.of(context).size.width > 900;

    return isDesktop
        ? Stack(
          alignment: Alignment.bottomCenter,
          children: [
            Container(height: 176, color: ColorOutlet.surface),
            ResponsivePaddingWrapper(
              child: Column(
                children: [
                  Row(
                    children: [
                      const CategoryGrid(),
                      SizedBox(height: 25, width: 104),
                      Flexible(
                        child: Padding(
                          padding: const EdgeInsets.only(bottom: 145),
                          child: CategoryFollowPromo(
                            onAppStorePressed: onAppStorePressed,
                            onGooglePlayPressed: onGooglePlayPressed,
                          ),
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        )
        : Container(
          decoration: BoxDecoration(
            gradient: LinearGradient(
              begin: Alignment.bottomCenter,
              end: Alignment.topCenter,
              stops: [0.0, 0.178, 0.178, 1.0],
              colors: [ColorOutlet.surface, ColorOutlet.surface, Colors.transparent, Colors.transparent],
            ),
          ),
          child: ResponsivePaddingWrapper.mobile(maxWidth: 512.0, minPadding: 16, child: CategoryGrid(isMobile: true)),
        );
  }
}

Color getFilteredColor(Color color) {
  return Color.alphaBlend(const Color(0xFFFFFFFF).withValues(alpha: 0.56), color);
}
