import 'package:flutter/material.dart';

import '../../../../../core/theme/color_outlet.dart';
import '../../../../../core/theme/text_pattern.dart';
import 'categories_section.dart';

class CategoryItemWidget extends StatelessWidget {
  final String imagePath;
  final String title;
  final Color color;
  final double paddingTop;
  final double? width;
  final double scale;

  const CategoryItemWidget({
    super.key,
    this.paddingTop = 80,
    this.width,
    this.scale = 1.0,
    required this.color,
    required this.imagePath,
    required this.title,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: width ?? 180 * scale,
      child: Container(
        padding: EdgeInsets.fromLTRB(24 * scale, 21 * scale, 0, 0),
        decoration: BoxDecoration(color: getFilteredColor(color), borderRadius: BorderRadius.circular(24 * scale)),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          mainAxisAlignment: MainAxisAlignment.end,
          children: [
            TextPattern.customText(
              text: title,
              fontSize: 24 * scale,
              fontWeightOption: FontWeightOption.bold,
              color: ColorOutlet.contentSecondary,
              textAlign: TextAlign.start,
            ),
            TextPattern.customText(
              text: '+1000 ofertas',
              fontSize: 16 * scale,
              fontWeightOption: FontWeightOption.regular,
              color: ColorOutlet.contentSecondary.withValues(alpha: 0.50),
              textAlign: TextAlign.start,
            ),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: [
                Padding(
                  padding: EdgeInsets.only(top: paddingTop * scale),
                  child: ClipRRect(
                    borderRadius: BorderRadius.only(bottomRight: Radius.circular(24 * scale)),
                    child: Image.asset(
                      imagePath,
                      height: (120 * scale).clamp(50.0, 128),
                      width: (120 * scale).clamp(50.0, 128),
                      fit: BoxFit.cover,
                      errorBuilder:
                          (context, error, stackTrace) => Container(
                            height: (80 * scale).clamp(50.0, 128),
                            width: (80 * scale).clamp(50.0, 128),
                            color: Colors.grey[200],
                            alignment: Alignment.center,
                            child: Icon(Icons.image, size: 40 * scale, color: Colors.grey),
                          ),
                    ),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }
}
