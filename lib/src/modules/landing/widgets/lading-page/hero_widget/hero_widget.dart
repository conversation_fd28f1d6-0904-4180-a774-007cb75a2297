import 'package:flutter/material.dart';
import 'package:promobell_landing/src/core/theme/color_outlet.dart';

import '../../../../../core/widgets/buttons/store_buttons.dart';
import '../../../../../core/widgets/layout/responsive_padding_wrappe.dart';
import '../categories_section/action_tips_section.dart';
import '../categories_section/categories_section.dart';
import '../categories_section/category_follow_promo.dart';
import 'background_hero_widget.dart';
import 'hero_title.dart';
import 'promotional_banner_section.dart';

class HeroWidget extends StatelessWidget {
  final VoidCallback onAppStorePressed;
  final VoidCallback onGooglePlayPressed;

  const HeroWidget({super.key, required this.onAppStorePressed, required this.onGooglePlayPressed});

  @override
  Widget build(BuildContext context) {
    final isWide = MediaQuery.of(context).size.width > 900;
    return isWide
        ? _DesktopContent(onAppStore: onAppStorePressed, onGooglePlay: onGooglePlayPressed)
        : _MobileContent(onAppStore: onAppStorePressed, onGooglePlay: onGooglePlayPressed);
  }
}

class _DesktopContent extends StatelessWidget {
  final VoidCallback onAppStore;
  final VoidCallback onGooglePlay;

  const _DesktopContent({required this.onAppStore, required this.onGooglePlay});

  @override
  Widget build(BuildContext context) {
    return BackgroundHeroWidget(
      heroContent: ResponsivePaddingWrapper(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const SizedBox(height: 96),
            const HeroTitle(),
            const SizedBox(height: 32),
            StoreButtons(onAppStore: onAppStore, onGooglePlay: onGooglePlay),
            const SizedBox(height: 184),
          ],
        ),
      ),
      bodyContent: ResponsivePaddingWrapper(
        child: Row(
          mainAxisAlignment: MainAxisAlignment.start,
          children: [
            Column(children: [const SizedBox(height: 136), ActionTipsSection(), const SizedBox(height: 88)]),
          ],
        ),
      ),
      categories: CategoriesSection(onAppStorePressed: onAppStore, onGooglePlayPressed: onGooglePlay),
      mobileImageOverlay: ResponsivePaddingWrapper(
        rightPadding: true,
        stack: Positioned(
          left: -255,
          top: 57,
          child: Row(mainAxisAlignment: MainAxisAlignment.end, children: [PromotionalBannerSection()]),
        ),
      ),
    );
  }
}

class _MobileContent extends StatelessWidget {
  final VoidCallback onAppStore;
  final VoidCallback onGooglePlay;

  const _MobileContent({required this.onAppStore, required this.onGooglePlay});

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        BackgroundHeroWidget(
          isMobile: true,
          heroContent: Column(
            children: [
              ResponsivePaddingWrapper.mobile(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const SizedBox(height: 48),
                    const HeroTitle(fontSize: 48, isMobile: true),
                    const SizedBox(height: 30),
                    StoreButtons(onAppStore: onAppStore, onGooglePlay: onGooglePlay),
                    const SizedBox(height: 56),
                  ],
                ),
              ),
              PromotionalBannerSection(isMobile: true),
            ],
          ),
          categories: CategoriesSection(onAppStorePressed: onAppStore, onGooglePlayPressed: onGooglePlay),
          bodyContent: ResponsivePaddingWrapper.mobile(child: ActionTipsSection(isMobile: true, center: true)),
        ),
        Container(
          color: ColorOutlet.surface,
          child: ResponsivePaddingWrapper.mobile(
            child: CategoryFollowPromo(
              isMobile: true,
              onAppStorePressed: onAppStore,
              onGooglePlayPressed: onGooglePlay,
            ),
          ),
        ),
      ],
    );
  }
}
