import 'package:flutter/material.dart';

import '../../../../../core/theme/color_outlet.dart';
import 'hero_background.dart';

class BackgroundHeroWidget extends StatelessWidget {
  final Widget? heroContent;
  final Widget? bodyContent;
  final Widget? mobileImageOverlay;
  final Widget? categories;
  final double? height;
  final bool isMobile;

  const BackgroundHeroWidget({
    this.heroContent,
    this.bodyContent,
    this.mobileImageOverlay,
    this.isMobile = false,
    this.categories,
    this.height,
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        Column(
          children: [
            HeroBackground(isMobile: isMobile, child: heroContent),
            Container(
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  begin: Alignment.bottomCenter,
                  end: Alignment.topCenter,
                  colors: [ColorOutlet.paper, ColorOutlet.surface],
                ),
              ),
              child: Column(children: [bodyContent ?? Container(), categories ?? Container()]),
            ),
          ],
        ),
        if (!isMobile) mobileImageOverlay ?? Container(),
      ],
    );
  }
}
