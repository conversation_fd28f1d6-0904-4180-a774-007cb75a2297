import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';

import '../../../../../core/theme/svg_icons.dart';
import '../../../../../core/widgets/layout/responsive_padding.dart';
import 'brand_header_info.dart';
import 'promobell_avatar.dart';

class WhatsChannelHeader extends StatelessWidget {
  const WhatsChannelHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return ResponsivePadding(
      minPadding: 10,
      maxPadding: 88,
      paddingBuilder: (padding) => EdgeInsets.only(right: padding),
      breakpoints: {600: 0.02, 900: 0.04, 1200: 0.06, double.infinity: 0.08},
      child: Container(
        height: 70,
        width: 336,
        padding: EdgeInsets.all(16),
        decoration: BoxDecoration(
          color: Color(0xFF00A884),
          borderRadius: BorderRadius.only(topLeft: Radius.circular(21), topRight: Radius.circular(21)),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            PromobellAvatar(),
            <PERSON><PERSON><PERSON><PERSON>(width: 8),
            BrandHeaderInfo(),
            Spacer(),
            SvgPicture.asset(SvgIcons.notificationBroken, colorFilter: ColorFilter.mode(Colors.white, BlendMode.srcIn)),
          ],
        ),
      ),
    );
  }
}
