import 'package:flutter/material.dart';
import 'package:promobell_landing/src/modules/landing/widgets/lading-page/whats_app_section/offer_detail_card.dart';
import 'package:promobell_landing/src/modules/landing/widgets/lading-page/whats_app_section/social_reaction_tag.dart';

import '../../../../../core/theme/color_outlet.dart';
import '../../../../../core/theme/text_pattern.dart';
import '../../../../../core/widgets/layout/scroll_reveal.dart';
import '../hero_widget/hero_image.dart';

class ChannelHeaderMockup extends StatelessWidget {
  const ChannelHeaderMockup({super.key});

  @override
  Widget build(BuildContext context) {
    return Stack(
      clipBehavior: Clip.none,
      children: [
        BorderedPanel(height: 486, width: 422),
        Positioned(top: 16, left: 16, child: Image.asset('assets/images/home.png', width: 390, height: 470)),
        Positioned(
          top: 114,
          left: 188,
          child: Container(
            height: 22,
            width: 47,
            alignment: Alignment.center,
            decoration: BoxDecoration(
              color: Color(0x0f000000).withValues(alpha: 0.25),
              borderRadius: BorderRadius.circular(8),
            ),
            child: TextPattern.customText(
              text: 'HOJE',
              color: ColorOutlet.contentTertiary,
              fontSize: 12,
              fontFamilyOption: FontFamilyOption.roboto,
            ),
          ),
        ),
        Positioned(top: 146, left: 40, child: ScrollReveal(offset: Offset(0, 0.1), child: OfferDetailCard())),
        Positioned(top: 530, left: 52, child: ScrollReveal(offset: Offset(0, 1), child: SocialReactionTag())),
      ],
    );
  }
}
