import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';

import '../../../../../core/theme/color_outlet.dart';

class PromobellBrand extends StatelessWidget {
  const PromobellBrand({super.key});

  @override
  Widget build(BuildContext context) {
    return SvgPicture.asset(
      'assets/images/promobell.svg',
      height: 30,
      colorFilter: ColorFilter.mode(ColorOutlet.contentTertiary, BlendMode.srcIn),
      errorBuilder:
          (context, error, stackTrace) => Container(
            height: 30,
            width: 120,
            color: Colors.transparent,
            alignment: Alignment.center,
            child: const Text(
              'Promobell',
              style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold, fontSize: 18),
            ),
          ),
    );
  }
}
