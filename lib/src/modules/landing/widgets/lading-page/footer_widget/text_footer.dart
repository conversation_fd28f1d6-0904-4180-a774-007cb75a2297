import 'package:flutter/material.dart';

import '../../../../../core/theme/color_outlet.dart';
import '../../../../../core/theme/text_pattern.dart';

class TextFooter extends StatelessWidget {
  const TextFooter({super.key});

  @override
  Widget build(BuildContext context) {
    return Column(
      spacing: 8,
      children: [
        TextPattern.customText(
          text: '© Promobell. Todos os direitos reservados. ',
          fontSize: 14,
          fontWeightOption: FontWeightOption.regular,
          color: ColorOutlet.contentTertiary,
          textAlign: TextAlign.center,
        ),
        TextPattern.customText(
          text:
              'Todas as marcas mencionadas são propriedade de seus respectivos detentores: Amazon.com, Inc., Ebazar.com.br LTDA, Magazine Luiza S/A',
          fontSize: 11,
          fontWeightOption: FontWeightOption.regular,
          color: ColorOutlet.contentTertiary.withValues(alpha: 0.5),
          textAlign: TextAlign.center,
        ),
        TextPattern.customText(
          text: 'Versão 1.0.8',
          fontSize: 11,
          fontWeightOption: FontWeightOption.regular,
          color: ColorOutlet.contentDisabled.withAlpha(70),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }
}
