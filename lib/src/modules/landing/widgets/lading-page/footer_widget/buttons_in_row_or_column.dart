import 'package:flutter/material.dart';
import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell_landing/src/modules/landing/widgets/lading-page/footer_widget/promobell_brand.dart';

import '../../../../../core/theme/svg_icons.dart';
import '../../../../../core/widgets/buttons/safe_browsing_widget.dart';
import 'custom_text_button.dart';

class ButtonsInRowOrColumn extends StatelessWidget {
  final bool isRow;
  final Function() onInstagramPressed;

  const ButtonsInRowOrColumn({super.key, this.isRow = true, required this.onInstagramPressed});

  @override
  Widget build(BuildContext context) {
    return isRow
        ? Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            PromobellBrand(),
            SizedBox(width: 97),
            CustomTextButton(text: 'Sobre o Promobell', onPressed: () => Modular.to.pushNamed('/sobre')),
            SizedBox(width: 16),
            CustomTextButton(text: 'Termos de uso', onPressed: () => Modular.to.pushNamed('/termos')),
            SizedBox(width: 16),
            CustomTextButton(text: 'Política de Privacidade', onPressed: () => Modular.to.pushNamed('/privacidade')),
            SizedBox(width: 80),
            SafeBrowsingWidget(),
          ],
        )
        : FittedBox(
          fit: BoxFit.contain,
          child: Column(
            spacing: 24,
            children: [
              Row(
                spacing: 16,
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomTextButton(text: 'Sobre o Promobell', onPressed: () => Modular.to.pushNamed('/sobre')),
                  CustomTextButton(text: 'Termos de uso', onPressed: () => Modular.to.pushNamed('/termos')),
                  CustomTextButton(
                    text: 'Política de Privacidade',
                    onPressed: () => Modular.to.pushNamed('/privacidade'),
                  ),
                ],
              ),
              Row(mainAxisAlignment: MainAxisAlignment.center, children: [SafeBrowsingWidget()]),
              Row(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  CustomTextButton(
                    text: 'Promobelloficial',
                    icon: SvgIcons.instagram,
                    onPressed: onInstagramPressed,
                    comIcone: true,
                  ),
                ],
              ),
            ],
          ),
        );
  }
}
