import 'package:flutter_modular/flutter_modular.dart';
import 'package:promobell_landing/src/modules/landing/controller/landing_page_controller.dart';

import '../category/presentation/pages/category_page.dart';
import '../product/presentation/pages/product_page.dart';
import 'controller/privacy_page_controller.dart';
import 'page/about_the_promobell.dart';
import 'page/delete_acount_page.dart';
import 'page/landing_page.dart';
import 'page/privacy_page.dart';
import 'page/terms_of_use.dart';

class LandingPageModule extends Module {
  @override
  void routes(r) {
    r.child(
      '/',
      child:
          (context) => LandingPage(
            controller: Modular.get<LandingPageController>(),
          )..loadSeo(),
      transition: TransitionType.noTransition,
    );
    r.child(
      '/privacidade',
      child:
          (context) => PrivacyPage(
            controller: Modular.get<PrivacyPageController>(),
          )..loadSeo(),
      transition: TransitionType.noTransition,
    );
    r.child(
      '/sobre',
      child: (context) => AboutThePromobell()..loadSeo(),
      transition: TransitionType.noTransition,
    );
    r.child(
      '/termos',
      child: (context) => TermsOfUse()..loadSeo(),
      transition: TransitionType.noTransition,
    );
    r.child(
      '/excluir-conta',
      child: (context) => DeleteAccountPage(),
      transition: TransitionType.noTransition,
    );
    r.child(
      '/product',
      child: (context) => ProductPage(),
      transition: TransitionType.noTransition,
    );
    r.child(
      '/category',
      child: (context) => CategoryPage(),
      transition: TransitionType.noTransition,
    );
  }
}
