import 'package:dartz/dartz.dart';
import 'package:promobell_landing/src/core/enums/enums_errors.dart';
import 'package:promobell_landing/src/modules/category/domain/models/category_menu.dart';
import 'package:promobell_landing/src/modules/category/domain/repositories/i_category_repository.dart';
import 'package:promobell_landing/src/modules/category/infra/repositories/i_category_datasource.dart';

import '../../domain/models/category_details.dart';

class CategoryRepositoryImpl implements ICategoryRepository {
  final ICategoryDatasource datasource;

  CategoryRepositoryImpl(this.datasource);

  @override
  Future<Either<CategoryError, CategoryMenu>> getCategoryDetails(int id) async {
    try {
      final category = await datasource.getCategory(id);

      if (category != null) {
        return Right(category);
      }

      return Left(CategoryError.categoryNotFound);
    } catch (e) {
      return Left(CategoryError.errorLoading);
    }
  }

  @override
  Future<Either<CategoryError, CategoryDetails>> getCategoryDetailsComplete(int id) async {
    try {
      final category = await datasource.getCategory(id);
      if (category == null) return Left(CategoryError.categoryNotFound);

      final followers = await datasource.getCategoryFollowersCount(category.id);
      final products = await datasource.getTotalProductsByCategory(category.nome);

      final details = CategoryDetails(category, followers, products);
      return Right(details);
    } catch (e) {
      return Left(CategoryError.errorLoading);
    }
  }
}
