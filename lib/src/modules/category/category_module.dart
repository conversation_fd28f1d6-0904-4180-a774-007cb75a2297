import 'package:flutter_modular/flutter_modular.dart';

import '../../core/bindings/core_module.dart';
import 'domain/repositories/i_category_repository.dart';
import 'domain/repositories/i_get_category.dart';
import 'domain/usecase/get_category_details.dart';
import 'infra/data/category_datasource_impl.dart';
import 'infra/repositories/category_repository_impl.dart';
import 'infra/repositories/i_category_datasource.dart';

class CategoryModule extends Module {
  @override
  List<Module> get imports => [CoreModule()];

  @override
  void exportedBinds(i) {
    i.addSingleton<ICategoryDatasource>(CategoryDatasourceImpl.new);
    i.addSingleton<ICategoryRepository>(CategoryRepositoryImpl.new);
    i.addSingleton<IGetCategoryDetails>(GetCategoryDetails.new);
  }
}
