import 'package:dartz/dartz.dart';
import 'package:promobell_landing/src/modules/category/domain/models/category_menu.dart';

import '../../../../core/enums/enums_errors.dart';
import '../models/category_details.dart';

abstract class ICategoryRepository {
  Future<Either<CategoryError, CategoryMenu>> getCategoryDetails(int id);
  Future<Either<CategoryError, CategoryDetails>> getCategoryDetailsComplete(int id);
}
