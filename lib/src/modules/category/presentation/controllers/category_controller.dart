import 'package:flutter/widgets.dart';
import 'package:promobell_landing/src/modules/category/domain/models/category_menu.dart';
import 'package:promobell_landing/src/modules/category/domain/repositories/i_get_category.dart';

class CategoryController with ChangeNotifier {
  final IGetCategoryDetails _getCategoryDetails;

  CategoryController({required IGetCategoryDetails getCategoryDetails}) : _getCategoryDetails = getCategoryDetails;

  bool _loading = false;
  bool get loading => _loading;

  CategoryMenu? _category;
  CategoryMenu? get category => _category;

  String? _error;
  String? get error => _error;

  int _followers = 0;
  int get followers => _followers;

  int _products = 0;
  int get products => _products;

  Future<void> loadCategoryData(int? id) async {
    if (id == null) {
      _error = 'ID inválido';
      _category = null;
      _followers = 0;
      _products = 0;
      notifyListeners();
      return;
    }

    _loading = true;
    notifyListeners();

    final result = await _getCategoryDetails(id);
    result.fold(
      (error) {
        _error = 'Erro: $error';
        _category = null;
        _followers = 0;
        _products = 0;
      },
      (details) {
        _error = null;
        _category = details.category;
        _followers = details.followers;
        _products = details.products;
      },
    );

    _loading = false;
    notifyListeners();
  }
}
