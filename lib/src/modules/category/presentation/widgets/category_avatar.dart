import 'package:flutter/material.dart';
import 'package:promobell_landing/src/core/theme/color_outlet.dart';

class CategoryAvatar extends StatelessWidget {
  const CategoryAvatar({super.key, required this.categoryColor, required this.fotoPequena});

  final Color categoryColor;
  final String fotoPequena;

  @override
  Widget build(BuildContext context) {
    return Align(
      alignment: Alignment.topCenter,
      child: Padding(
        padding: const EdgeInsets.only(top: 32),
        child: Container(
          height: 80,
          width: 80,
          padding: const EdgeInsets.only(top: 32, left: 32),
          decoration: BoxDecoration(
            color: categoryColor,
            borderRadius: BorderRadius.circular(24),
            border: Border.all(color: ColorOutlet.paper, width: 4),
          ),
          child: ClipRRect(
            borderRadius: const BorderRadius.only(bottomRight: Radius.circular(20)),
            child: Image.asset(fotoPequena, width: 48, height: 48),
          ),
        ),
      ),
    );
  }
}
