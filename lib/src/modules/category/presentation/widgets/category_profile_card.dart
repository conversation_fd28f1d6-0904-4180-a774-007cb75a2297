import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:promobell_landing/src/core/helpers/format_helper.dart';
import 'package:promobell_landing/src/core/theme/color_outlet.dart';
import 'package:promobell_landing/src/core/theme/svg_icons.dart';
import 'package:promobell_landing/src/core/theme/text_pattern.dart';
import 'package:promobell_landing/src/modules/category/presentation/widgets/followers_or_offers_column.dart';

class CategoryProfileCard extends StatelessWidget {
  const CategoryProfileCard({
    super.key,
    required this.nome,
    required this.bio,
    required this.followers,
    required this.products,
  });

  final String nome;
  final String bio;
  final int followers;
  final int products;

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 240,
      width: 390,
      decoration: BoxDecoration(color: ColorOutlet.paper, borderRadius: BorderRadius.circular(24)),
      child: Column(
        children: [
          const SizedBox(height: 56),
          Row(
            spacing: 8,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              TextPattern.customText(text: nome, fontSize: 20, fontWeightOption: FontWeightOption.bold),
              SvgPicture.asset(
                SvgIcons.markerVerifiedFilled,
                colorFilter: ColorFilter.mode(ColorOutlet.contentPrimary, BlendMode.srcIn),
              ),
            ],
          ),
          const Spacer(),
          ConstrainedBox(
            constraints: const BoxConstraints(maxWidth: 310),
            child: TextPattern.customText(text: bio, fontSize: 14, textAlign: TextAlign.center, maxLines: 3),
          ),
          const Spacer(),
          Row(
            children: [
              const Spacer(),
              FollowersOrOffersColumn(title: formatNumber(followers), subtitle: 'Seguidores'),
              const SizedBox(width: 8),
              FollowersOrOffersColumn(title: formatNumber(products), subtitle: 'Ofertas'),
              const Spacer(),
            ],
          ),
          const SizedBox(height: 32),
        ],
      ),
    );
  }
}
