import 'package:flutter/material.dart';
import 'package:promobell_landing/src/core/theme/color_outlet.dart';
import 'package:promobell_landing/src/core/theme/text_pattern.dart';

class FollowersOrOffersColumn extends StatelessWidget {
  final String title;
  final String subtitle;

  const FollowersOrOffersColumn({required this.title, required this.subtitle, super.key});

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      width: 151,
      child: Column(
        children: [
          TextPattern.customText(
            text: title,
            fontSize: 16,
            color: ColorOutlet.contentPrimary,
            fontWeightOption: FontWeightOption.semiBold,
          ),
          TextPattern.customText(text: subtitle, fontSize: 12),
        ],
      ),
    );
  }
}
